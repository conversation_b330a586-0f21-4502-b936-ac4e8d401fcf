"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const common_1 = require("@nestjs/common");
const review_schema_1 = require("../database/schemas/review.schema");
let ReviewRepository = class ReviewRepository {
    _reviewModel;
    _logger;
    constructor(_reviewModel, _logger) {
        this._reviewModel = _reviewModel;
        this._logger = _logger;
    }
    async create(Review) {
        try {
            this._logger.log('[ReviewRepository:create] - Creating Review');
            const createdReview = new this._reviewModel(Review);
            const result = (await createdReview.save());
            this._logger.log(`[ReviewRepository:create] - Review with id: ${result?._id} created successfully.`);
            return result;
        }
        catch (error) {
            this._logger.error(error, '[ReviewRepository:create] - Failed to create Review');
            throw new Error(error?.message);
        }
    }
    async update(review) {
        try {
            this._logger.log('[ReviewRepository:update] - Updating Review');
            const updatedData = {
                rating: review.rating,
                comment: review.comment,
            };
            const updatedReview = (await this._reviewModel
                .findByIdAndUpdate(review._id, updatedData, { new: true })
                .exec());
            this._logger.log(`[ReviewRepository:update] - Review with id: ${review?.productId} updated successfully.`);
            return updatedReview;
        }
        catch (error) {
            this._logger.error(error, '[ReviewRepository:update] - Failed to update Review');
            throw new Error(error?.message);
        }
    }
    async delete(id) {
        try {
            this._logger.log('[ReviewRepository:delete] - Deleting Review');
            await this._reviewModel.findByIdAndDelete(id).exec();
            this._logger.log('[ReviewRepository:delete] - Review deleted');
        }
        catch (error) {
            this._logger.error(error, '[ReviewRepository:create] - Failed to create Review');
            throw new Error(error.message || 'Failed to delete Review');
        }
    }
    findAll() {
        try {
            this._logger.log('[ReviewRepository:findAll] - Retrieving all Reviews');
            const Reviews = this._reviewModel.find().exec();
            this._logger.log('[ReviewRepository:findAll] - Reviews retrieved');
            return Reviews;
        }
        catch (error) {
            this._logger.error(error, '[ReviewRepository:findAll] - Failed to retrieve Reviews');
            throw new Error(error.message || 'Failed to create Review');
        }
    }
    findByProductId(id) {
        this._logger.log('[ReviewRepository:findAll] - Retrieving Review with id: ' + id);
        const Review = this._reviewModel.find({ productId: id }).exec();
        this._logger.log('[ReviewRepository:findAll] - Review retrieved');
        return Review;
    }
    findById(id) {
        this._logger.log('[ReviewRepository:findAll] - Retrieving Review with id: ' + id);
        const review = this._reviewModel.findById(id).exec();
        this._logger.log('[ReviewRepository:findAll] - Review retrieved');
        return review;
    }
    async getAverageRatingByProductId(productId) {
        try {
            this._logger.log('[ReviewRepository:getAverageRatingByProductId] - Retrieving average rating for product with id: ' +
                productId);
            const averageRating = (await this._reviewModel.aggregate([
                { $match: { productId: productId } },
                { $group: { _id: null, averageRating: { $avg: '$rating' } } },
            ]));
            this._logger.log('[ReviewRepository:getAverageRatingByProductId] - Average rating retrieved');
            return averageRating.length > 0
                ? averageRating[0]?.averageRating.toFixed(2) || '0'
                : '0';
        }
        catch (error) {
            this._logger.error(error, '[ReviewRepository:getAverageRatingByProductId] - Failed to retrieve average rating');
            throw new Error(error.message || 'Failed to retrieve average rating');
        }
    }
};
ReviewRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(review_schema_1.ReviewEntitySchema.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        common_1.Logger])
], ReviewRepository);
exports.default = ReviewRepository;
//# sourceMappingURL=review.repository.js.map